// Demo.cpp : Defines the entry point for the console application.
//
#if defined(_WIN32)
#include "windows.h"
#endif

#include <direct.h> 
#include <io.h>
#include "stdafx.h"
#include "malloc.h"
#include "stdlib.h"
#include "stdio.h"
#include "string.h"
#include "iostream"
using namespace std;

#include "AnalyzeDataNewInterface.h"

#define BUFFER_SIZE        320 * 1024                       //320k
#define HEADER_SIZE        40                              //40字节头
#define MAX_FILE_NAME_LEN  500                             //待解析的视频文件路径名最大值
#define PSTSPACKETTYPE     2                               //ps ts 封装文件
#define RTPPACKETTYPE      4                               //rtp 封装文件

void __stdcall CallbackFun(ANA_ERROR_INFOR* pErrorInfor, void* pUser)
{
    int retErr = pErrorInfor->nErrorType;
}

/**	@fn	      void CIDemux::InitConsole()
 *	@brief	  初始化控制台
 *	@param -I 
 *	@param -O 
 *	@return	  
 */
void InitConsole()
{
	HANDLE hOut       = GetStdHandle(STD_OUTPUT_HANDLE); 
	if (NULL == hOut)
	{
		return;
	}

	//重新设置缓冲区大小	
	COORD xyCoordinate = {615,32766};
	SetConsoleScreenBufferSize(hOut,xyCoordinate); 

	////设置文本颜色
	SetConsoleTextAttribute(hOut,FOREGROUND_GREEN);

	//设置窗口大小
	SMALL_RECT rc = {0,0, 160-1, 50-1}; 
	// 重置窗口位置和大小 
	SetConsoleWindowInfo(hOut,true ,&rc); 
}

int main (int argc , char * argv[] )
{ 
	InitConsole();

	unsigned char* pWriteBuf = NULL;
	pWriteBuf = (unsigned char*)malloc(sizeof(char) * BUFFER_SIZE);  //320k

	FILE * input_file       = NULL;
	void * m_hNewHandle     = 0;
	unsigned char* pReadBuf = NULL;
	int getbytes            = 0;
	unsigned int nVideoCnt  = 0;
	unsigned int nAudioCnt  = 0;
	unsigned int nPrivtCnt  = 0;

    int a = HIKANA_GetVersion();
    printf("version = %d\n",a);

	/* Open the input file. */
    char fileName[1314];
	char dirName[1314];
	memset (&fileName, 0, sizeof(fileName));   //初始化
	cout<<"输入需要解析的文件路径"<<endl;      //输入需要解析的文件路径
	gets (fileName);    
	//scanf("%s",fileName);
	if ((input_file = fopen(fileName, "rb")) == NULL) //打开需解析的文件
	{
		printf("can't open %s\n", argv[1]);
		goto EXIT;
	}

	/*sprintf((char*)(dirName),"%s.analyze",fileName);
	if(_access(dirName,0) == -1)
	{
		if(_mkdir(dirName) == -1)
		{
			printf("创建失败\n");
			exit(0);
		}
		printf("创建成功\n");
	}
	//分析结果文件
	sprintf((char*)(dirName),"%s.analyze\\result.txt",fileName);
	FILE* pF         = fopen(dirName,"w");     //解析结果
	sprintf((char*)(dirName),"%s.analyze\\video.luo",fileName);
	FILE* pVideoFile = fopen(dirName, "wb");   //保存视频裸数据文件
	sprintf((char*)(dirName),"%s.analyze\\aduio.luo",fileName);
	FILE* pAudioFile = fopen(dirName,"wb");         //保存音频数据
	sprintf((char*)(dirName),"%s.analyze\\RTPSequenceNo.txt",fileName);
    FILE* pRTPSeqNo  = fopen(dirName, "w");//rtp包序号
	*/
	//分析结果文件
	sprintf((char*)(dirName),"result.txt");    
	FILE* pF         = fopen(dirName,"w");     //解析结果
    sprintf((char*)(dirName),"all.luo");
    FILE* pAll       = fopen(dirName,"wb");
	sprintf((char*)(dirName),"video.luo");
	FILE* pVideoFile = fopen(dirName, "wb");   //保存视频裸数据文件
	sprintf((char*)(dirName),"aduio.luo");
	FILE* pAudioFile = fopen(dirName,"wb");         //保存音频数据
	sprintf((char*)(dirName),"RTPSequenceNo.txt");
	FILE* pRTPSeqNo  = fopen(dirName, "w");//rtp包序号
    FILE* pPrivtFile = fopen("Privt.dat","wb");         //保存音频数据

	pReadBuf = (unsigned char*)malloc(sizeof(char) * BUFFER_SIZE);
	if (NULL == pReadBuf)
	{
		printf("malloc failed\n");
		goto EXIT;
	}
	//带40字节的海康头文件
	fread(pReadBuf , 1 , 40/*HEADER_SIZE */, input_file);
	// 创建帧分析库句柄  ，  注意此处的pReadBuf中 是 头四十字节
	// 参数 1 设置帧分析库内部缓冲，4cif 以下一般设置 1 兆 ，大于 4cif 可调大该值
	// 参数 2 设置文件头四十个字节，实时流由设备端提供，本地文件一般为文件头四十字节
	// 创建成功返回句柄，后面对帧分析库的操作都依赖于这个句柄
	m_hNewHandle = HIKANA_CreateStreamEx(1024 * 1024, pReadBuf);//2M缓存创建句柄

	unsigned int nBaseFrameNum  = 0;
	unsigned int nBaseTimeStamp = 0;
	unsigned int nLastTimeStamp = 0;
	unsigned int i              = 0;
    unsigned int nRtpSeqNo      = 0;     //rtp包序号
	unsigned int nRtpPts        = 0;     //rtp时间戳
	int nMarkBIT                = 0;     //RTPmark标记位
	int nPT                     = 0;     //rtp包类型PT值
	int uRTPSize                = 0;     //rtp包长度
	unsigned int ssrc           = 0;     //ssrc

	/*测试HIKANA_RegistStreamInforCB接口返回的码流错误类型*/
	//void* pUser = NULL;
	//int retss = HIKANA_RegistStreamInforCB(m_hNewHandle, CallbackFun, pUser);

	//int retsr = HIKANA_ClearBuffer(m_hNewHandle);
	//int retsr = HIKANA_GetVersion();

//	fseek(input_file,0,SEEK_SET);
 //   unsigned long nRemainDataLen = 2 * 1024 *1024;     //解析缓存剩余数据大小
 //   unsigned char* pRemainData  = NULL;
	//pRemainData = (unsigned char*)malloc(sizeof(char) * 2 * 1024 *1024);//2M
	//if (NULL == pRemainData)
	//{
	//	printf("malloc failed\n");
	//	goto EXIT;
	//}

    //用于标记是ps/ts/hik文件还是rtp文件
    unsigned char  ucPsTsRtpFlag = PSTSPACKETTYPE;//初始化为0
    if ((2 == pReadBuf[8]) || (3 == pReadBuf[8]) || (1 == pReadBuf[8]))//40海康头中文件为ps/ts/hik封装类型
    {
        ucPsTsRtpFlag = PSTSPACKETTYPE;//为ps/ts文件
    } 
    if (4 == pReadBuf[8])                       //40海康头中文件为rtp封装类型
    {
        ucPsTsRtpFlag = RTPPACKETTYPE;//为rtp文件
    }

	// 在此利用文件 进行实时流的模拟	
	while (1)
	{
		PACKET_INFO_EX stPacket = {0};

		//ucPsTsRtpFlag = PSTSPACKETTYPE;
        /* ***    PS/TS 流送数据格式	**** */
       if (PSTSPACKETTYPE == ucPsTsRtpFlag)
        {
            getbytes = fread(pReadBuf, 1, BUFFER_SIZE, input_file);	// 读320k数据	
            //getbytes = fread(pReadBuf, 1, 10, input_file);	// 读320k数据	
            if (getbytes == 0)
            {                
                unsigned long nRemain = BUFFER_SIZE;
                unsigned char* pReadRemainBuf = (unsigned char*)malloc(sizeof(char) * BUFFER_SIZE);
                if (NULL == pReadBuf)
                {
                    printf("malloc failed\n");
                    goto EXIT;
                }
                HIKANA_GetRemainData(m_hNewHandle,pReadRemainBuf,&nRemain);
                fwrite(pReadRemainBuf, 1, nRemain, pAll);
                fflush(pAll);
                printf("nRemain = %d.\n",nRemain);

                break;
            }
            // 输入数据，在实时流中将获取的数据直接塞进来就可以
            //int ret = HIKANA_InputData(m_hNewHandle, pReadBuf, 10);//每次送320k
            int ret = HIKANA_InputData(m_hNewHandle, pReadBuf, getbytes);//每次送320k
            //int nRet = HIKANA_GetLastErrorH(m_hNewHandle);
            HIKANA_SetOutputPacketType(m_hNewHandle, ANALYZE_ENCAPSULATED_DATA);// 设置输出帧为裸数据(1:不带封装；0:为默认,带封装)
        }

		/*******  RTP 格式送数据解析   ********/
        if (RTPPACKETTYPE == ucPsTsRtpFlag)
        {
            getbytes = fread(pReadBuf, 1, 4, input_file);//读包前面的4字节长度
            if (getbytes != 4)
            {
                break;
            }
  
        // RTP包前长度为4个字节时  
            uRTPSize  =  pReadBuf[3];    //由于长度4个字节存储方式为小端(高-高,低-低),所以得到其值从高地址到低地址
            uRTPSize <<= 8;
            uRTPSize  |= pReadBuf[2];
            uRTPSize <<= 8;
            uRTPSize  |= pReadBuf[1];
            uRTPSize <<= 8;
            uRTPSize  |= pReadBuf[0];/*/**/

			            
            /*getbytes = fread(pReadBuf, 1, 4, input_file);//读包前面的4字节长度
            if (getbytes != 4)
            {
                break;
            }
            uRTPSize -= 4;//*/
            //////////////////////////////////////////////////////////////////////
            //RTP包前长度吃屎了
			/*uRTPSize  =  pReadBuf[3];    //由于长度4个字节存储方式为小端(高-高,低-低),所以得到其值从高地址到低地址
			uRTPSize <<= 8;
			uRTPSize  |= pReadBuf[2];
            uRTPSize -= 4;//*/

            //////////////////////////////////////////////////////////////////////
            //RTP包前长度吃屎了...........................................
			uRTPSize  =  pReadBuf[2];    //由于长度4个字节存储方式为小端(高-高,低-低),所以得到其值从高地址到低地址
			uRTPSize <<= 8;
			uRTPSize  |= pReadBuf[3];
            //uRTPSize -= 4;

            // 分析TCP协议的RTP封装的码流时(RTP包前的长度字节为后2个字节,不是4个字节)
              //uRTPSize  =  pReadBuf[3];    //
              //uRTPSize <<= 8;
              //uRTPSize  |= pReadBuf[2];
              //uRTPSize -= 4;            //且长度包含了包前面的4个字节,需减去

            getbytes = fread(pReadBuf, 1, uRTPSize, input_file);//读一个rtp包到内存

           ///////////////////写rtp包序号到文件///////////////////////////////
			nMarkBIT  = (pReadBuf[1] & 0x80) >> 7;    //mark 标记位
			nRtpPts   = ((pReadBuf[4] << 24) + (pReadBuf[5] << 16) + (pReadBuf[6] << 8) + pReadBuf[7]);//包时间戳
            //nRtpPts   /= 90;
            nRtpSeqNo = (pReadBuf[2] << 8) + pReadBuf[3];       //rtp包序号
			nPT       = (pReadBuf[1] & 0x7f);                     //包PT值
			ssrc      = ((pReadBuf[8] << 24) + (pReadBuf[9] << 16) + (pReadBuf[10] << 8) + pReadBuf[11]);//ssrc

			int nLen = sprintf((char*)(pWriteBuf),"RtpSeqNo:\t%u\tMarkBIT:\t%u\t Pts:\t%d\tPT:\t%x\t PackLen:\t%d\t Ssrc:\t%x \n", nRtpSeqNo, nMarkBIT, nRtpPts/90, nPT,uRTPSize, ssrc);
            fwrite(pWriteBuf, 1, nLen, pRTPSeqNo);              //将rtp包序号写到txt文件
            fflush(pRTPSeqNo);
            memset(pWriteBuf, 0, BUFFER_SIZE); 

            HIKANA_InputData(m_hNewHandle, pReadBuf, getbytes); //送一个rtp包到内部解析
        }
         
		/* 获取剩余数据*/
	//	int nRet = HIKANA_GetRemainData(m_hNewHandle, pRemainData, &nRemainDataLen);
     //   int nRet = HIKANA_GetLastErrorH(m_hNewHandle);
	//	HIKANA_SetAnalyzeFrameType(m_hNewHandle, ANALYZE_TEMPORAL_LAYER_1);


		/////////////////////////取每一帧数据/////////////////////////////////////////

		// 及时从帧分库中取帧，有的话返回该帧的信息，没有返回获取失败
		// 实际操作中HIKANA_InputData 和 HIKANA_GetOnePacketEx可以在不同线程中，只要对应相同的句柄就可以
		int rettt = 0;
		while (rettt == 0)
		{   //打印信息根据需要在PACKET_INFO_EX结构体中选择需要打印的信息/////
			rettt = HIKANA_GetOnePacketEx(m_hNewHandle, &stPacket);
            if(stPacket.dwTimeStamp == 72752)
            {
                printf("");
            }
            if(0 == rettt)
            {
                if (stPacket.dwPacketSize == 0)
                {
                    printf("");
                }
            }
			//printf("%x\n",rettt);
			if(rettt != 0)
			{
                rettt = HIKANA_GetLastErrorH(m_hNewHandle);
                printf("HIKANA_GetOnePacketEx:last error = %d.\n",rettt);
				break;
			}
			if ((stPacket.nPacketType == VIDEO_I_FRAME)||(stPacket.nPacketType == VIDEO_P_FRAME)||(stPacket.nPacketType == VIDEO_B_FRAME))//只取视频
			{
				static unsigned int stat_dwTimeStamp = 0;				

				printf("[%05d]Video frame, timestamp = %u,timestamp_diff = %u!\n", nVideoCnt,stPacket.dwTimeStamp,stPacket.dwTimeStamp-stat_dwTimeStamp);

				stat_dwTimeStamp = stPacket.dwTimeStamp;

				fwrite(stPacket.pPacketBuffer, 1, stPacket.dwPacketSize, pVideoFile);//得到视频裸数据
				//只打印视频帧信息
				int nLen = sprintf((char*)(pWriteBuf),"[%05d]%d\t%d\t%u\t%u\t%d\t%d\t%d\t%d\t smart = %d\t,globaltime=%d-%d-%d-%d-%d-%d-%d!\n", 
					nVideoCnt,stPacket.nPacketType, stPacket.dwFrameNum, stPacket.dwTimeStamp,stPacket.dwTimeStampHigh,stPacket.dwFrameRate,
					stPacket.dwPacketSize, stPacket.uWidth, stPacket.uHeight,(stPacket.Reserved[5] && 0x00000001)/*smart264标记*/,
					stPacket.nYear, stPacket.nMonth, stPacket.nDay, stPacket.nHour, stPacket.nMinute, stPacket.nSecond,stPacket.nMillisecond);
				nVideoCnt++;
				fwrite(pWriteBuf, 1, nLen, pF);
				fflush(pF);
				memset(pWriteBuf, 0, BUFFER_SIZE); 

                if(nVideoCnt == 7600)
                {
                    printf("");
                }
			}
			else if (stPacket.nPacketType == AUDIO_PACKET)
			{
				fwrite(stPacket.pPacketBuffer, 1, stPacket.dwPacketSize, pAudioFile);//得到音频数据

				static unsigned int stat_dwTimeStamp = 0;				

				printf("[%05d]Audio frame, timestamp = %u,timestamp_diff = %u!\n", nAudioCnt,stPacket.dwTimeStamp,stPacket.dwTimeStamp-stat_dwTimeStamp);

				stat_dwTimeStamp = stPacket.dwTimeStamp;

				int nLen = sprintf((char*)(pWriteBuf),"[%05d]%d\t%d\t%u\t%d\t%d\t%d\t%d\t%d\t,globaltime=%d-%d-%d-%d-%d-%d-%d!\n", 
					nAudioCnt,stPacket.nPacketType, stPacket.dwFrameNum, stPacket.dwTimeStamp, stPacket.dwFrameRate,
					stPacket.dwPacketSize, stPacket.uWidth, stPacket.uHeight,(stPacket.Reserved[5] && 0x00000001)/*深P标记*/,
					stPacket.nYear, stPacket.nMonth, stPacket.nDay, stPacket.nHour, stPacket.nMinute, stPacket.nSecond,stPacket.nMillisecond);
				nAudioCnt++;
				fwrite(pWriteBuf, 1, nLen, pF);
				fflush(pF);
				memset(pWriteBuf, 0, BUFFER_SIZE); 
			}
			else if (stPacket.nPacketType == PRIVT_PACKET)
			{
				printf("[%05d]Privt frame, timestamp = %u!\n", nPrivtCnt,stPacket.dwTimeStamp);
				fwrite(stPacket.pPacketBuffer, 1, stPacket.dwPacketSize, pPrivtFile);//得到私有帧数据
                fflush(pPrivtFile);
				int nLen = sprintf((char*)(pWriteBuf),"[%05d]%d\t%d\t%u\t%d\t%d\t%d\t%d\t%d\t reseverd[3] = %d,globaltime=%d-%d-%d-%d-%d-%d-%d!\n", 
					nPrivtCnt,stPacket.nPacketType, stPacket.dwFrameNum, stPacket.dwTimeStamp, stPacket.dwFrameRate,
					stPacket.dwPacketSize, stPacket.uWidth, stPacket.uHeight,(stPacket.Reserved[5] && 0x00000001)/*深P标记*/,stPacket.Reserved[3],
					stPacket.nYear, stPacket.nMonth, stPacket.nDay, stPacket.nHour, stPacket.nMinute, stPacket.nSecond,stPacket.nMillisecond);
				nPrivtCnt++;
				fwrite(pWriteBuf, 1, nLen, pF);
				fflush(pF);
				memset(pWriteBuf, 0, BUFFER_SIZE); 

				//switch(stPacket.Reserved[0])  // 智能结构信息
				//{
				//case INTELLIGENT_IVS_INDEX:
				//	{   // 裸数据地址，兼容64位系统
				//		unsigned char* p = (unsigned char*)((((long)stPacket.Reserved[1]) << 32) + stPacket.Reserved[2]);						
				//		break;
				//	}
				//default:
				//	{
				//		////
				//		break;
				//	}
				//}

			}
			else              
			{
				//
				printf("unxepected type %d.\n",stPacket.nPacketType);
			}

	        //if (stPacket.nPacketType == VIDEO_I_FRAME) // 只取I帧数据
			//{
			//    fwrite(stPacket.pPacketBuffer, 1, stPacket.dwPacketSize, pVideoFile);
			//}

            fwrite(stPacket.pPacketBuffer, 1, stPacket.dwPacketSize, pAll);
            fflush(pAll);

		}
	}

EXIT:
	if (m_hNewHandle)
	{
		HIKANA_Destroy(m_hNewHandle);
	}
	if (pReadBuf)
	{
		free(pReadBuf);
	}
    if (pWriteBuf)
    {
        free(pWriteBuf);
    }
	if (input_file)
	{
		fclose(input_file);
	}
    if (pAll)
    {
        fclose(pAll);
    }
	if (pVideoFile)
	{
		fclose(pVideoFile);
	}
	if (pAudioFile)
	{
		fclose(pAudioFile);
	}
    if (pRTPSeqNo)
    {
        fclose(pRTPSeqNo);
    }
    if (pPrivtFile)
    {
        fclose(pPrivtFile);
    }
	system("pause");
	return 0;
}